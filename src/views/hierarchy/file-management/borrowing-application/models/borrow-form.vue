<template>
    <alert-content :on-default-save="handleSave">
        <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
        >
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="申请人" :span="12">
                    <n-input v-model:value="formData.applicant" placeholder="张三" readonly />
                </n-form-item-gi>

                <n-form-item-gi label="申请日期" :span="12">
                    <n-date-picker
                        v-model:value="formData.applyDate"
                        type="date"
                        format="yyyy-MM-dd"
                        placeholder="2025-05-02"
                        disabled
                        class="w-full"
                    />
                </n-form-item-gi>

                <n-form-item-gi label="借阅日期" path="borrowPeriod" :span="24">
                    <n-date-picker
                        v-model:value="formData.borrowPeriod"
                        type="daterange"
                        format="yyyy-MM-dd"
                        placeholder="开始日期-结束日期"
                        class="w-full"
                    />
                </n-form-item-gi>

                <n-form-item-gi label="借阅原因" path="borrowReason" :span="24">
                    <n-select
                        v-model:value="formData.borrowReason"
                        :options="borrowReasonOptions"
                        placeholder="请选择"
                        class="w-200px"
                    />
                    <n-input
                        v-if="formData.borrowReason === 'other'"
                        v-model:value="formData.otherReason"
                        placeholder="请输入其他原因"
                        class="ml-10px flex-1"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="24" path="distributeList">
                    <div class="flex-v w-100%">
                        <div class="flex justify-between">
                            <span class="ml-2px mb-10px required-field">借阅清单</span>
                            <n-button type="primary" size="small" @click="handleAddFile">添加文件</n-button>
                        </div>
                        <vxe-table
                            show-overflow
                            :edit-config="{
                                trigger: 'click',
                                mode: 'cell'
                            }"
                            :data="formData.fileList"
                        >
                            <vxe-column type="seq" title="序号" width="70"></vxe-column>
                            <vxe-column
                                field="status"
                                title="文件有效性"
                                width="120"
                                :edit-render="{ name: 'VxeSelect', options: statusOptions, placeholder: '请点击选择' }"
                                :formatter="({ cellValue }) => formatStaticOptionLabel(statusOptions, cellValue)"
                            ></vxe-column>
                            <vxe-column
                                field="fileType"
                                title="文件类型"
                                width="120"
                                :edit-render="{
                                    name: 'VxeSelect',
                                    options: typeOptions,
                                    placeholder: '请点击选择',
                                    events: { change: handleFileTypeChange }
                                }"
                            >
                               
                            </vxe-column>
                            <vxe-column
                                field="fileCategory"
                                title="文件类别"
                                width="120"
                                :edit-render="{ placeholder: '请点击选择' }"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'categoryOptions', row.fileCategory)"
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.fileCategory"
                                        :options="getRowOptions(row.id, 'categoryOptions')"
                                        @change="handleCategoryChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="fileName"
                                title="文件名称"
                                width="120"
                                :edit-render="{ placeholder: '请点击选择' }"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'fileNameOptions', row.fileName)"
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.fileName"
                                        :options="getRowOptions(row.id, 'fileNameOptions')"
                                        placeholder="请点击选择"
                                        @change="handleFileNameChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="fileNumber"
                                title="文件编号"
                                width="120"
                                :edit-render="{ placeholder: '请点击选择' }"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'fileNumberOptions', row.fileNumber)"
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.fileNumber"
                                        :options="getRowOptions(row.id, 'fileNumberOptions')"
                                        placeholder="请点击选择"
                                        @change="handleFileNumberChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="fileVersion"
                                title="版本/版次"
                                width="120"
                                :edit-render="{ placeholder: '请点击选择' }"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'fileVersionOptions', row.fileVersion)"
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.fileVersion"
                                        :options="getRowOptions(row.id, 'fileVersionOptions')"
                                        placeholder="请点击选择"
                                        @change="handleFileVersionChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column field="todo" title="操作" width="120">
                                <template v-slot="{ row }">
                                    <n-button size="tiny" type="error" @click="handleRemoveFile(row)">删除</n-button>
                                </template>
                            </vxe-column>
                        </vxe-table>
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { NButton, NInput, NSelect, type FormRules } from 'naive-ui';
import { BorrowingApplicationForm } from '@/api/apis/nebula/api/v1/borrowing-application';
import { VxeTable, VxeColumn } from 'vxe-table';
import { VxeSelect } from 'vxe-pc-ui';

const props = defineProps<{
    row?: BorrowingApplicationForm;
}>();

const emit = defineEmits<{
    (e: 'submit'): void;
}>();

const formRef = ref();

// 表单数据
const formData = ref<BorrowingApplicationForm>({
    applicant: '张三',
    applyDate: Date.now(),
    borrowPeriod: null,
    borrowReason: '',
    otherReason: '',
    fileList: [
        {
            id: '1',
            fileType: '',
            fileCategory: '',
            fileName: '',
            fileNumber: '',
            fileVersion: '',
            status: ''
        },
        {
            id: '2',
            fileType: '',
            fileCategory: '',
            fileName: '',
            fileNumber: '',
            fileVersion: '',
            status: ''
        }
    ]
});

// 为每行单独管理选项数据
const rowOptionsMap = ref<Map<string, {
    categoryOptions: Record<string, any>[];
    fileNameOptions: Record<string, any>[];
    fileNumberOptions: Record<string, any>[];
    fileVersionOptions: Record<string, any>[];
}>>(new Map());

// 借阅原因选项
const borrowReasonOptions = [
    { label: '工作需要', value: 'work' },
    { label: '学习研究', value: 'study' },
    { label: '项目参考', value: 'project' },
    { label: '其他', value: 'other' }
];

// 表单验证规则
const rules: FormRules = {
    borrowPeriod: [{ required: true, message: '请输入借阅日期', trigger: ['blur', 'change'], type: 'array' }],
    borrowReason: [{ required: true, message: '请选择借阅原因', trigger: ['blur', 'change'] }]
};

// 添加文件
const handleAddFile = () => {
    const newId = String(Date.now());
    formData.value.fileList?.push({
        id: newId,
        fileType: '',
        fileCategory: '',
        fileName: '',
        fileNumber: '',
        fileVersion: '',
        status: ''
    });

    // 为新行初始化选项数据
    initRowOptions(newId);
};

// 删除文件
const handleRemoveFile = (row: any) => {
    const index = formData.value.fileList?.findIndex(item => item.id === row.id);
    if (index !== undefined && index >= 0) {
        formData.value.fileList?.splice(index, 1);
        // 清理对应的选项数据
        rowOptionsMap.value.delete(row.id);
    }
};

// 初始化行选项数据
const initRowOptions = (rowId: string) => {
    rowOptionsMap.value.set(rowId, {
        categoryOptions: [],
        fileNameOptions: [],
        fileNumberOptions: [],
        fileVersionOptions: []
    });
};

// 获取指定行的选项数据
const getRowOptions = (rowId: string, optionType: string) => {
    const rowOptions = rowOptionsMap.value.get(rowId);
    if (!rowOptions) {
        initRowOptions(rowId);
        return [];
    }
    return rowOptions[optionType as keyof typeof rowOptions] || [];
};

// 格式化选项标签 - 将 id 转换为对应的 label 显示
const formatOptionLabel = (rowId: string, optionType: string, value: string) => {
    if (!value) return '请点击选择';

    const options = getRowOptions(rowId, optionType);
    const option = options.find(opt => opt.value === value);
    return option ? option.label : value;
};

// 状态选项
const statusOptions = [
    { label: '有效', value: 'effective' },
    { label: '作废', value: 'cancel' }
];

// 类型选项
const typeOptions = [
    { label: '内部文件', value: 'internal' },
    { label: '外部文件', value: 'external' }
];
const handleFileTypeChange = async (row: any, rowIndex: any) => {
    console.log("🚀 ~ handleFileTypeChange ~ row:", row)
    try {
        const dict = await api.sass.api.v1.dict.get('file_business_dictionary');
        let categoryId = '';
        switch (rowIndex.value) {
            case 'internal':
                categoryId = dict.data.data[0].extra.fileCategory.internal;
                break;
            case 'external':
                categoryId = dict.data.data[0].extra.fileCategory.external;
                break;
        }
        if (!categoryId) {
            window.$message.error('获取文件类别树字典数据失败');
            return;
        }
        const res = await $apis.nebula.api.v1.businessDictionary.node.tree({
            id: categoryId
        });

        // 更新指定行的类别选项
        const rowOptions = rowOptionsMap.value.get(row.row.id);
        if (rowOptions) {
            rowOptions.categoryOptions = $utils.treeData.convertTreeData(res.data);
        }

        // 清空该行的文件类别选择
        const fileItem = formData.value.fileList?.find(item => item.id === row.row.id);
        if (fileItem) {
            fileItem.fileCategory = '';
            fileItem.fileName = '';
            fileItem.fileNumber = '';
            fileItem.fileVersion = '';
        }
    } catch (error) {
        console.error('获取文件类别失败:', error);
        window.$message.error('获取文件类别失败');
    }
};

// 文件类别变更处理
const handleCategoryChange = async (event: any, row: any) => {
    const categoryValue = event?.value || event;
    const rowId = row?.id || row;

    try {
        // 根据选择的文件类别获取对应的文件名称选项
        // 这里需要根据实际API调整
        const res = await $apis.nebula.api.v1.businessDictionary.node.tree({
            id: categoryValue
        });

        // 更新指定行的文件名称选项
        const rowOptions = rowOptionsMap.value.get(rowId);
        if (rowOptions) {
            rowOptions.fileNameOptions = $utils.treeData.convertTreeData(res.data);
        }

        // 清空该行的后续选择
        const fileItem = formData.value.fileList?.find(item => item.id === rowId);
        if (fileItem) {
            fileItem.fileName = '';
            fileItem.fileNumber = '';
            fileItem.fileVersion = '';
        }
    } catch (error) {
        console.error('获取文件名称选项失败:', error);
        window.$message.error('获取文件名称选项失败');
    }
};

// 文件名称变更处理
const handleFileNameChange = async (event: any, row: any) => {
    const fileNameValue = event?.value || event;
    const rowId = row?.id || row;

    try {
        // 根据选择的文件名称获取对应的文件编号选项
        // 这里使用模拟数据，实际项目中需要替换为真实API
        // const res = await $apis.nebula.api.v1.file.getFileNumbers({
        //     fileName: fileNameValue
        // });

        // 模拟数据
        const mockFileNumbers = [
            { label: `编号-${fileNameValue}-001`, value: `${fileNameValue}-001` },
            { label: `编号-${fileNameValue}-002`, value: `${fileNameValue}-002` },
            { label: `编号-${fileNameValue}-003`, value: `${fileNameValue}-003` }
        ];

        // 更新指定行的文件编号选项
        const rowOptions = rowOptionsMap.value.get(rowId);
        if (rowOptions) {
            rowOptions.fileNumberOptions = mockFileNumbers;
        }

        // 清空该行的后续选择
        const fileItem = formData.value.fileList?.find(item => item.id === rowId);
        if (fileItem) {
            fileItem.fileNumber = '';
            fileItem.fileVersion = '';
        }
    } catch (error) {
        console.error('获取文件编号选项失败:', error);
        window.$message.error('获取文件编号选项失败');
    }
};

// 文件编号变更处理
const handleFileNumberChange = async (event: any, row: any) => {
    const fileNumberValue = event?.value || event;
    const rowId = row?.id || row;

    try {
        // 根据选择的文件编号获取对应的版本选项
        // 这里使用模拟数据，实际项目中需要替换为真实API
        // const res = await $apis.nebula.api.v1.file.getFileVersions({
        //     fileNumber: fileNumberValue
        // });

        // 模拟数据
        const mockFileVersions = [
            { label: `版本-${fileNumberValue}-v1`, value: `${fileNumberValue}-v1` },
            { label: `版本-${fileNumberValue}-v2`, value: `${fileNumberValue}-v2` },
            { label: `版本-${fileNumberValue}-v3`, value: `${fileNumberValue}-v3` }
        ];

        // 更新指定行的版本选项
        const rowOptions = rowOptionsMap.value.get(rowId);
        if (rowOptions) {
            rowOptions.fileVersionOptions = mockFileVersions;
        }

        // 清空该行的版本选择
        const fileItem = formData.value.fileList?.find(item => item.id === rowId);
        if (fileItem) {
            fileItem.fileVersion = '';
        }
    } catch (error) {
        console.error('获取文件版本选项失败:', error);
        window.$message.error('获取文件版本选项失败');
    }
};

// 文件版本变更处理
const handleFileVersionChange = async (event: any, row: any) => {
    const fileVersionValue = event?.value || event;
    const rowId = row?.id || row;

    console.log('文件版本选择:', fileVersionValue, '行ID:', rowId);
    // 这里可以添加版本选择后的逻辑
};

// 保存表单
const handleSave = async () => {
    await formRef.value?.validate();

    try {
        // 暂时使用模拟API，实际项目中需要替换为真实API
        if (props.row?.id) {
            // 编辑
            console.log('编辑借阅申请:', formData.value);
            window.$message.success('修改成功');
        } else {
            // 新增
            console.log('新增借阅申请:', formData.value);
            window.$message.success('新增成功');
        }
        emit('submit');
    } catch (error) {
        console.error('保存失败:', error);
    }
};

// 初始化数据
onMounted(() => {
    if (props.row) {
        formData.value = { ...props.row };
    }

    // 为现有的文件列表项初始化选项数据
    formData.value.fileList?.forEach(item => {
        if (item.id) {
            initRowOptions(item.id);
        }
    });
});
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>
